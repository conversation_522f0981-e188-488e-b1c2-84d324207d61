export default Category;
declare const Category: mongoose.Model<{
    name: string;
    image: string;
}, {}, {}, {}, mongoose.Document<unknown, {}, {
    name: string;
    image: string;
}, {}, mongoose.DefaultSchemaOptions> & {
    name: string;
    image: string;
} & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, {
    name: string;
    image: string;
}, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    name: string;
    image: string;
}>, {}, mongoose.ResolveSchemaOptions<mongoose.DefaultSchemaOptions>> & mongoose.FlatRecord<{
    name: string;
    image: string;
}> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
import mongoose from 'mongoose';
//# sourceMappingURL=category.d.ts.map