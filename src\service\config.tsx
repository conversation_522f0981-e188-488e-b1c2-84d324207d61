import { Platform } from "react-native"

// VPS PRODUCTION CONFIGURATION - FULLY OPERATIONAL ✅
// Using your network IP for development, VPS for production
const DEVELOPMENT_IP = '************'; // Your current network IP
const VPS_IP_URL = 'http://***************:3000'; // Direct VPS IP (working)
const VPS_HTTPS_URL = 'https://api.goatgoat.xyz'; // VPS subdomain with SSL (preferred)
const RENDER_BACKUP_URL = 'https://client-d9x3.onrender.com'; // Backup Render URL

// Environment detection
const IS_DEVELOPMENT = __DEV__;
const USE_CLOUD = true; // Set to true for production deployment

// Dynamic URL configuration
const getBaseURL = () => {
  if (USE_CLOUD) {
    // Use HTTPS VPS URL after SSL installation, fallback to HTTP IP
    return `${VPS_HTTPS_URL}/api`;
    // Fallback: return `${VPS_IP_URL}/api`; // Use if HTTPS not working
  }

  if (Platform.OS === 'android') {
    return IS_DEVELOPMENT
      ? `http://${DEVELOPMENT_IP}:3000/api`  // Use network IP for real device
      : 'http://********:3000/api';         // Use emulator localhost
  }

  return `http://${DEVELOPMENT_IP}:3000/api`; // iOS/other platforms
};

const getSocketURL = () => {
  if (USE_CLOUD) {
    // Use HTTPS VPS URL after SSL installation, fallback to HTTP IP
    return VPS_HTTPS_URL;
    // Fallback: return VPS_IP_URL; // Use if HTTPS not working
  }

  if (Platform.OS === 'android') {
    return IS_DEVELOPMENT
      ? `http://${DEVELOPMENT_IP}:3000`      // Use network IP for real device
      : 'http://********:3000';             // Use emulator localhost
  }

  return `http://${DEVELOPMENT_IP}:3000`;   // iOS/other platforms
};

export const BASE_URL = getBaseURL();
export const SOCKET_URL = getSocketURL();
export const GOOGLE_MAP_API = "AIzaSyDOBBimUu_eGMwsXZUqrNFk3puT5rMWbig"
export const BRANCH_ID ='68a1a76e2c93ad61799983b3'

// Debug logging
if (__DEV__) {
  console.log('🔧 API Configuration:');
  console.log('📡 BASE_URL:', BASE_URL);
  console.log('🔌 SOCKET_URL:', SOCKET_URL);
  console.log('🌍 Platform:', Platform.OS);
  console.log('☁️ Using Cloud:', USE_CLOUD);
}

